package com.wifochina.modules.oauth.dto

import com.alibaba.fastjson2.annotation.JSONField
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Created on 2024/11/12 13:55.
 * <AUTHOR>
 */
data class AccountListPage(
    val count: Int, val `data`: List<Data>?
)

data class Data(
    val allResourceStat: Int,
    val authStat: Int,
    val countryOrRegionCode: Int,
    val countryOrRegionName: String,
    val ct: Long,
    val email: String,
    val expireAt: Long,
    val inheritResourceStat : Int,
    val level: Int,
    val mark: String,
    val openAPIAuthStat: Int,
    val parentEmail: String,
    val parentRoleId: String,
    val parentUserId: String,
    val roleCategory: String,
    val resourceCount: Int?,
    val resource: Resource?,
    val roleId: String,
    val roleName: String,
    val userId: String,
    val userName: String,
    val userNickName: String
)

data class Resource(
    val name: String?,
)