alter table t_group
    add day_report_data_interval int default 60 comment '日报间隔时间(Min)';

alter table `t_ammeter`
    add `connect_type` varchar(50) DEFAULT 'SERIAL_PORT' COMMENT '连接方式(串口serial_port或者web_socket)';

alter table `t_ammeter`
    add `ws_device_type` varchar(50) DEFAULT null COMMENT 'ws_device_type';

alter table `t_ammeter`
    add `ws_device_name` varchar(50) DEFAULT null COMMENT 'ws_device_name';



ALTER TABLE t_strategy_control
    CHANGE COLUMN price_continue_duration_hour price_continue_duration_segment INT(11) DEFAULT 0 COMMENT '电价连续段数';
ALTER TABLE t_strategy_template_item_control
    CHANGE COLUMN price_continue_duration_hour price_continue_duration_segment INT(11) DEFAULT 0 COMMENT '电价连续段数';


ALTER TABLE t_group
    ADD `time_sharing_back_flow_limit_controller` boolean default false  COMMENT '分时防逆流开关';

ALTER TABLE t_group
    ADD `time_sharing_demand_controller` boolean default false  COMMENT '分时控需开关';


CREATE TABLE `t_time_sharing_demand`
(
    `id`                   int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id`           varchar(128) DEFAULT NULL COMMENT '项目id',
    `group_id`             varchar(128) DEFAULT NULL COMMENT '分组id',
    `system_strategy_id`   int NOT NULL COMMENT '分组对应的主策略的id',
    `start_time`           time         DEFAULT NULL,
    `end_time`             time         DEFAULT NULL,
    `demand_item_controller` boolean      DEFAULT false COMMENT 'item需量开关',
    `demand_month_control_power` double DEFAULT 0.0 COMMENT '月初需量power',
    `demand_control_power` double DEFAULT 0.0 COMMENT '需量power',
    `create_by`            varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time`          bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by`            varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time`          bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
);

-- 告警配置表
CREATE TABLE `t_alarm_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` varchar(128) NOT NULL COMMENT '项目ID',
    `alarm_content` tinyint(1) DEFAULT NULL COMMENT '告警内容',
    `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0:禁用 1:启用)',
    `profit_deviation_coefficient` float DEFAULT NULL COMMENT '收益偏差系数',
    `downtime_threshold` int(11) DEFAULT NULL COMMENT '停机时长阈值(分钟)',
    `offline_count_threshold` int(11) DEFAULT NULL COMMENT '离线状态次数',
    `efficiency_reminder_threshold` float DEFAULT NULL COMMENT '效率提醒阈值',
    `create_by` varchar(128) DEFAULT NULL COMMENT '创建者',
    `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(128) DEFAULT NULL COMMENT '修改者',
    `update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警配置表';