package com.wifochina.modules.event.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.modules.event.entity.HideEventCodeEntity;
import com.wifochina.modules.event.entity.MeterEventCodeEntity;
import com.wifochina.modules.event.enums.HideCodeLabelEnums;
import com.wifochina.modules.event.request.CommonEventCodeRequest;
import com.wifochina.modules.event.request.HideCodePageRequest;
import com.wifochina.modules.event.request.HideCodeRequest;
import com.wifochina.modules.event.service.HideEventCodeService;
import com.wifochina.modules.event.service.MeterEventCodeService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectExtEntity;
import com.wifochina.modules.project.service.ProjectExtService;
import com.wifochina.modules.user.info.LogInfo;
import com.wifochina.modules.user.service.LogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 告警信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@RestController
@RequestMapping("/event")
@Api(value = "event", tags = "13-事件通知告警")
public class MeterEventCodeController {

    @Resource private MeterEventCodeService meterEventCodeService;

    @Resource private HideEventCodeService hideEventCodeService;

    @Resource private ProjectExtService projectExtService;

    @Resource private LogService logService;

    @PostMapping("/eventMeterCode/get")
    @ApiOperation("查询eventMeterCode")
    @PreAuthorize("hasAuthority('/system/showHideCode')")
    public Result<IPage<MeterEventCodeEntity>> getEventCodeEntity(
            @RequestBody CommonEventCodeRequest commonEventCodeRequest) {
        return Result.success(
                meterEventCodeService.queryEventMessage(
                        Page.of(
                                commonEventCodeRequest.getPageNum(),
                                commonEventCodeRequest.getPageSize()),
                        commonEventCodeRequest));
    }

    @Transactional
    @PostMapping("/hideMeterCode/add")
    @ApiOperation("增加屏蔽的eventMeterCode")
    @PreAuthorize("hasAuthority('/system/operateHideCode')")
    @Log(module = "EVENT", methods = "EVENT_METER_HIDE", type = OperationType.ADD_SIMPLE)
    public Result<Object> saveHideCode(@RequestBody HideCodeRequest hideCodeRequest) {
        if (hideCodeRequest.getHideAll()) {
            // 屏蔽所有  把标志位放到 项目上
            projectExtService
                    .lambdaUpdate()
                    .set(ProjectExtEntity::getHideAllMeterCode, true)
                    .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                    .update();
        } else {
            // 屏蔽部分
            // 或者 解除屏蔽  都是先删除old , 再插入, 如果是解除全部屏蔽 则hideCodeRequest不传 
            projectExtService
                    .lambdaUpdate()
                    .set(ProjectExtEntity::getHideAllMeterCode, false)
                    .eq(ProjectExtEntity::getId, WebUtils.projectId.get())
                    .update();
            List<HideEventCodeEntity> list =
                    hideCodeRequest.getEventCode() == null
                            ? new ArrayList<>()
                            : hideCodeRequest.getEventCode().stream()
                                    .map(
                                            e ->
                                                    new HideEventCodeEntity(
                                                            WebUtils.projectId.get(),
                                                            e,
                                                            String.valueOf(
                                                                    HideCodeLabelEnums.METER_DEVICE
                                                                            .getLabel())))
                                    .collect(Collectors.toList());
            hideEventCodeService.remove(
                    Wrappers.lambdaQuery(HideEventCodeEntity.class)
                            .eq(HideEventCodeEntity::getProjectId, WebUtils.projectId.get())
                            .eq(
                                    HideEventCodeEntity::getType,
                                    String.valueOf(HideCodeLabelEnums.METER_DEVICE.getLabel())));
            hideEventCodeService.saveBatch(list);
        }

        return Result.success();
    }

    @Transactional
    @PostMapping("/hideMeterCode/list")
    @ApiOperation("查询屏蔽的eventMeterCode")
    @PreAuthorize("hasAuthority('/system/showHideCode')")
    public Result<Object> listHideCode(@RequestBody HideCodePageRequest hideCodePageRequest) {
        IPage<MeterEventCodeEntity> page =
                hideEventCodeService.getHideMeterEventCode(
                        WebUtils.projectId.get(), hideCodePageRequest);
        ProjectExtEntity projectExtEntity = projectExtService.getById(WebUtils.projectId.get());
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Map<String, Object> result;
            if (page != null) {
                // 将 page 转为 Map
                result = objectMapper.convertValue(page, new TypeReference<>() {});
            } else {
                result = new HashMap<>();
            }
            // 增加自定义属性
            result.put("hideAllMeterCode", projectExtEntity.getHideAllMeterCode());
            // 返回带有额外属性的结果
            return Result.success(result);
        } catch (Exception e) {
            return Result.failure("转换失败: " + e.getMessage());
        }
    }
}
