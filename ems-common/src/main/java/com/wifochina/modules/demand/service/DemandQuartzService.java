package com.wifochina.modules.demand.service;

/**
 * <AUTHOR>
 * @since 2024-01-15 7:27 PM
 */
public interface DemandQuartzService {

    /** 添加定时任务 */
    void addDemandCalcJob(String projectId);

    /** 添加定时任务 */
    void rescheduleDemandCalcJob(String projectId);

    /** 删除定时任务 */
    void delDemandCalcJob(String projectId);

    /** 暂停定时任务 */
    void pauseDemandCalcJob(String projectId);

    /** 继续定时任务 */
    void resumeDemandCalcJob(String projectId);

    /** 添加定时任务 */
    void addDemandMonthInitJob(String projectId);

    /** 删除定时任务 */
    void delDemandMonthInitJob(String projectId);

    void rescheduleDemandMonthInitJob(String projectId);

    /** 添加定时任务 */
    void addCapacityJob(String timeZone);
}
