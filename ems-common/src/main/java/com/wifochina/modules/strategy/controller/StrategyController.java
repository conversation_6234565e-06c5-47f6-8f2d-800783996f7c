package com.wifochina.modules.strategy.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wifochina.common.constants.CapacityAlarmTypeEnum;
import com.wifochina.common.constants.ElectricPriceTypeEnum;
import com.wifochina.common.constants.ErrorResultCode;
import com.wifochina.common.exception.ServiceException;
import com.wifochina.common.log.Log;
import com.wifochina.common.page.Result;
import com.wifochina.common.time.MyTimeUtil;
import com.wifochina.common.util.EmsConstants;
import com.wifochina.common.util.StrategyUtil;
import com.wifochina.common.util.StringUtil;
import com.wifochina.common.util.VoltageModeEnum;
import com.wifochina.modules.capacity.entity.CapacityAlarmRecordEntity;
import com.wifochina.modules.capacity.service.CapacityAlarmRecordService;
import com.wifochina.modules.demand.entity.DemandLogEntity;
import com.wifochina.modules.demand.service.DemandLogService;
import com.wifochina.modules.demand.service.DemandQuartzService;
import com.wifochina.modules.group.entity.GroupEntity;
import com.wifochina.modules.group.request.go.GroupGoRequest;
import com.wifochina.modules.group.request.go.Strategy;
import com.wifochina.modules.group.request.go.YearlyStrategy;
import com.wifochina.modules.group.service.GroupService;
import com.wifochina.modules.log.OperationType;
import com.wifochina.modules.log.detailresolveservice.TimeSharingBackFlowLimitLogDetailService;
import com.wifochina.modules.log.detailresolveservice.StrategyLogDetailService;
import com.wifochina.modules.oauth.util.WebUtils;
import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.strategy.entity.TimeSharingBackFlowLimitEntity;
import com.wifochina.modules.strategy.entity.StrategyEntity;
import com.wifochina.modules.strategy.request.*;
import com.wifochina.modules.strategy.service.TimeSharingBackFlowLimitService;
import com.wifochina.modules.strategy.service.StrategyService;

import com.wifochina.modules.strategytemplate.common.StrategyCommonUtils;
import com.wifochina.modules.strategytemplate.converter.StrategyControlConverter;
import com.wifochina.modules.strategytemplate.entity.StrategyControlEntity;
import com.wifochina.modules.strategytemplate.request.StrategyControlRequest;
import com.wifochina.modules.strategytemplate.service.*;
import com.wifochina.modules.strategytemplate.service.predicate.DefaultCustomModelStrategy;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.*;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@RequestMapping("/strategy")
@RestController
@Api(tags = "09-控制策略")
@RequiredArgsConstructor
public class StrategyController {

    private final StrategyService strategyService;
    private final StrategyControlService strategyControlService;

    private final GroupService groupService;

    private final DemandLogService demandLogService;
    private final CapacityAlarmRecordService capacityAlarmRecordService;

    private final DemandQuartzService demandQuartzService;

    private final ProjectService projectService;
    private final DefaultCustomModelStrategy defaultCustomModelStrategy;
    private final StrategyServiceKt strategyServiceKt;
    private final StrategyImportService strategyImportService;
    private final TimeSharingBackFlowLimitService timeSharingBackFlowLimitService;

    /** 删除时段配置 */
    @PostMapping("/deleteTimeSharingDemandItem")
    @ApiOperation("删除时间策略")
    @Log(
            module = "STRATEGY",
            methods = "STRATEGY_TIME_DEL",
            type = OperationType.DEL,
            // TODO change logDetailServiceClass
            logDetailServiceClass = TimeSharingBackFlowLimitLogDetailService.class)
    @PreAuthorize("hasAuthority('/strategy/time/delete')")
    public Result<Object> deleteTimeSharingDemandItem(
            @ApiParam(value = "分时需量控制id", required = true) @RequestParam("id") Integer id) {
        if (id == null || id == 0) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        //        backFlowLimitService.removeById(id);
        return Result.success();
    }

    @PostMapping("/addTimeSharingDemandItem")
    @ApiOperation("新增 分时需量item策略")
    @Log(module = "STRATEGY", methods = "STRATEGY_TIME_ADD", type = OperationType.ADD)
    public Result<Void> addTimeSharingDemandItem(
            @RequestBody TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest) {
        timeSharingBackFlowLimitRequest.setProjectId(WebUtils.projectId.get());
        // TODO 分时需量item add
        //        backFlowLimitService.addBackFlowLimitItem(backFlowLimitRequest);
        return Result.success();
    }

    /** 修改时段配置 */
    @PostMapping("/editBackFlowLimitItem")
    @ApiOperation("修改分时防逆流")
    @Log(module = "STRATEGY", methods = "STRATEGY_TIME_UPDATE", type = OperationType.UPDATE)
    @PreAuthorize("hasAuthority('/strategy/time/edit')")
    public Result<Object> editBackFlowLimitItem(
            @RequestBody TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest) {

        TimeSharingBackFlowLimitEntity timeSharingBackFlowLimitEntity =
                timeSharingBackFlowLimitService.getById(timeSharingBackFlowLimitRequest.getId());
        if (timeSharingBackFlowLimitEntity == null
                || timeSharingBackFlowLimitRequest.getId() == null
                || timeSharingBackFlowLimitEntity.getId() == 0) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        if (timeSharingBackFlowLimitRequest.getStartTime() == null
                || timeSharingBackFlowLimitEntity.getEndTime() == null) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        // TODO 这个方法 和下面的删除 测试一下 + upload 下发的 结构改一下  外加 大屏那边的 改一下 分时防逆流 这个需求结束
        timeSharingBackFlowLimitRequest.setProjectId(WebUtils.projectId.get());
        timeSharingBackFlowLimitService.editBackFlowLimitItem(timeSharingBackFlowLimitRequest);
        return Result.success();
    }

    /** 删除时段配置 */
    @PostMapping("/deleteBackFlowLimitItem")
    @ApiOperation("删除时间策略")
    @Log(
            module = "STRATEGY",
            methods = "STRATEGY_TIME_DEL",
            type = OperationType.DEL,
            logDetailServiceClass = TimeSharingBackFlowLimitLogDetailService.class)
    @PreAuthorize("hasAuthority('/strategy/time/delete')")
    public Result<Object> deleteBackFlowLimitItem(
            @ApiParam(value = "分时防逆流id", required = true) @RequestParam("id") Integer id) {
        if (id == null || id == 0) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        timeSharingBackFlowLimitService.removeById(id);
        return Result.success();
    }

    @PostMapping("/addBackFlowLimitItem")
    @ApiOperation("新增backFlow 分时防逆流策略")
    @Log(module = "STRATEGY", methods = "STRATEGY_TIME_ADD", type = OperationType.ADD)
    public Result<Void> addBackFlowLimitItem(
            @RequestBody TimeSharingBackFlowLimitRequest timeSharingBackFlowLimitRequest) {
        timeSharingBackFlowLimitRequest.setProjectId(WebUtils.projectId.get());
        timeSharingBackFlowLimitService.addBackFlowLimitItem(timeSharingBackFlowLimitRequest);
        return Result.success();
    }

    /** 添加时段配置 */
    @PostMapping("/addTimeStrategy")
    @ApiOperation("新增时间策略")
    @Log(module = "STRATEGY", methods = "STRATEGY_TIME_ADD", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    public Result<Object> addTimeStrategy(@RequestBody StrategyTimeRequest strategyTimeRequest) {
        //        checkWeekDay(strategyTimeRequest);
        checkStrategyDate(strategyTimeRequest);
        checkTimeForSpecialiseStrategy(WebUtils.projectId.get(), strategyTimeRequest);
        checkPower(strategyTimeRequest);
        StrategyEntity strategy = new StrategyEntity();
        strategy.setId(null);
        strategy.setProjectId(WebUtils.projectId.get());
        BeanUtils.copyProperties(strategyTimeRequest, strategy);
        // 如果是海外
        if (strategyTimeRequest.getItemControl() != null) {
            StrategyControlRequest itemControl = strategyTimeRequest.getItemControl();
            if (!itemControl.getPriceBaseValueController()
                    && !itemControl.getPriceBenchmarkController()
                    && !itemControl.getPriceDifferenceController()) {
                if (itemControl.getPriceContinueDurationController()) {
                    throw new ServiceException(
                            ErrorResultCode.STRATEGY_CONTINUE_CONTROL_NEED_PRE.value());
                }
            }
            // 这里是新增 就把control 新增一条记录 然后id 绑定到这个 strategy上面
            StrategyControlEntity strategyControlEntity =
                    StrategyControlConverter.Companion.getINSTANCE()
                            .request2Entity(strategyTimeRequest.getItemControl());
            strategyControlEntity.setProjectId(WebUtils.projectId.get());
            strategyControlEntity.setGroupId(strategyTimeRequest.getGroupId());
            strategyControlEntity.setStrategyDate(strategyTimeRequest.getStrategyDate());
            strategyControlService.save(strategyControlEntity);
            strategy.setStrategyControlId(strategyControlEntity.getId());
        }
        strategyService.save(strategy);
        // 处理一下 control 相关
        return Result.success();
    }

    private void checkStrategyDate(StrategyTimeRequest strategyTimeRequest) {
        if (StringUtil.isEmpty(strategyTimeRequest.getStrategyDate())) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        // TODO 校验一下MM-dd格式 Whoever is willing to do it, who does it
    }

    private void checkPower(StrategyTimeRequest strategyTimeRequest) {
        if (strategyTimeRequest.getType().equals(0) || strategyTimeRequest.getType().equals(1)) {
            if (strategyTimeRequest.getPower() < 0) {
                // 1.4.0 需求新增 限制 充电 放电的 策略 power是不能是负数
                throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
            }
        }
    }

    private void checkTimeForSpecialiseStrategy(
            String projectId, StrategyTimeRequest strategyRequest) {
        ProjectEntity projectEntity = projectService.getById(projectId);
        if (projectEntity != null) {
            checkTime(projectEntity, strategyRequest);
        }
    }

    /** 修改时段配置 */
    @PostMapping("/editTimeStrategy")
    @ApiOperation("修改时间策略")
    @Log(module = "STRATEGY", methods = "STRATEGY_TIME_UPDATE", type = OperationType.UPDATE)
    @PreAuthorize("hasAuthority('/strategy/time/edit')")
    public Result<Object> editTimeStrategy(@RequestBody StrategyTimeRequest strategyTimeRequest) {
        StrategyEntity strategyEntity = strategyService.getById(strategyTimeRequest.getId());
        if (strategyEntity == null
                || strategyTimeRequest.getId() == null
                || strategyTimeRequest.getId() == 0) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        //        checkWeekDay(strategyTimeRequest);
        checkStrategyDate(strategyTimeRequest);
        checkTimeForSpecialiseStrategy(WebUtils.projectId.get(), strategyTimeRequest);
        StrategyEntity strategy = new StrategyEntity();
        BeanUtils.copyProperties(strategyTimeRequest, strategy);
        strategy.setProjectId(WebUtils.projectId.get());
        strategy.setVoltageMode(VoltageModeEnum.auto.getName());
        strategyService.updateById(strategy);
        if (strategyEntity.getStrategyControlId() != null
                && strategyTimeRequest.getItemControl() != null) {
            StrategyControlRequest itemControl = strategyTimeRequest.getItemControl();
            if (!itemControl.getPriceBaseValueController()
                    && !itemControl.getPriceBenchmarkController()
                    && !itemControl.getPriceDifferenceController()) {
                if (itemControl.getPriceContinueDurationController()) {
                    throw new ServiceException(
                            ErrorResultCode.STRATEGY_CONTINUE_CONTROL_NEED_PRE.value());
                }
            }
            StrategyControlEntity strategyControlEntity =
                    StrategyControlConverter.Companion.getINSTANCE()
                            .request2Entity(strategyTimeRequest.getItemControl());
            strategyControlEntity.setProjectId(WebUtils.projectId.get());
            strategyControlEntity.setGroupId(strategyTimeRequest.getGroupId());
            strategyControlEntity.setStrategyDate(strategyTimeRequest.getStrategyDate());
            strategyControlEntity.setId(strategy.getStrategyControlId());
            strategyControlService.saveOrUpdate(strategyControlEntity);
        }
        return Result.success();
    }

    /** 删除时段配置 */
    @PostMapping("/deleteTimeStrategy")
    @ApiOperation("删除时间策略")
    @Log(
            module = "STRATEGY",
            methods = "STRATEGY_TIME_DEL",
            type = OperationType.DEL,
            logDetailServiceClass = StrategyLogDetailService.class)
    @PreAuthorize("hasAuthority('/strategy/time/delete')")
    public Result<Object> deleteTimeStrategy(
            @ApiParam(value = "策略id，非分组id", required = true) @RequestParam("id") Integer id) {
        StrategyEntity strategy = strategyService.getById(id);
        if (strategy.getWeekDay() != null && strategy.getWeekDay() == 0) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        if (id == null || id == 0) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        Long strategyControlId = strategy.getStrategyControlId();
        if (strategyControlId != null) {
            // 删除 controlId
            strategyControlService.removeById(strategyControlId);
        }
        strategyService.removeById(id);
        return Result.success();
    }

    /** 修改分组设置 */
    @PostMapping("/editGroupStrategy")
    @ApiOperation("修改分组策略")
    @Log(module = "STRATEGY", methods = "STRATEGY_GROUP_UPDATE", type = OperationType.ADD)
    @PreAuthorize("hasAuthority('/strategy/edit')")
    public Result<Object> editGroupStrategy(
            @RequestBody StrategyGroupRequest strategyGroupRequest) {
        if (strategyGroupRequest.getId() == null || strategyGroupRequest.getId() == 0) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ID_NULL.value());
        }
        if (strategyGroupRequest.getSoc() != null
                && strategyGroupRequest.getSoc() > EmsConstants.SOC_MAX) {
            throw new ServiceException(ErrorResultCode.SOC_EXCEED.value());
        }
        // 2024-08-13 13:56:26 add check voltageModel use VoltageModeEnum
        if (strategyGroupRequest.getVoltageMode() != null
                && !StringUtil.isEmpty(strategyGroupRequest.getVoltageMode())) {
            try {
                VoltageModeEnum.valueOf(strategyGroupRequest.getVoltageMode());
                // 如果需要使用 mode 可以在这里处理
            } catch (IllegalArgumentException e) {
                // 如果传入的字符串不在枚举中，这里会抛出异常，你可以处理这个异常
                // 比如返回一个错误信息或抛出自定义异常
                throw new IllegalArgumentException(
                        "Invalid voltage mode: " + strategyGroupRequest.getVoltageMode());
            }
        }
        Optional.ofNullable(strategyGroupRequest.getPowerFactorControlValue())
                .ifPresent(
                        (e) -> {
                            double limitFactor = 0.9;
                            if (strategyGroupRequest.getPowerFactorControlValue() > 1
                                    || strategyGroupRequest.getPowerFactorControlValue()
                                            < limitFactor) {
                                throw new ServiceException(
                                        ErrorResultCode.POWER_FACTOR_CONTROL_VALUE_EXCEED.value());
                            }
                        });
        StrategyEntity oldStrategy = strategyService.getById(strategyGroupRequest.getId());
        StrategyEntity strategy = new StrategyEntity();
        BeanUtils.copyProperties(strategyGroupRequest, strategy);
        strategy.setProjectId(WebUtils.projectId.get());
        strategyService.updateById(strategy);
        if (0 == oldStrategy.getWeekDay()) {
            GroupEntity groupEntity = groupService.getById(oldStrategy.getGroupId());

            // 1.4.2 add 记录容量controlPower 数据变更
            changeControlPowerRecord(strategyGroupRequest, oldStrategy, groupEntity);
            // old. 记录 需量control变更的记录
            changeGirdControlPowerRecord(strategyGroupRequest, oldStrategy, groupEntity);
            if (strategyGroupRequest.getMonthControlPower() == null
                    ^ oldStrategy.getMonthControlPower() == null) {
                demandQuartzService.rescheduleDemandMonthInitJob(WebUtils.projectId.get());
            }
        }
        // 1.4.4 分时防逆流
        //        GroupEntity systemGroupEntity =
        // groupService.systemGroupEntity(WebUtils.projectId.get());
        //        if (systemGroupEntity != null
        //                &&
        // Boolean.TRUE.equals(systemGroupEntity.getTimeSharingBackFlowLimitController())) {
        //            List<BackFlowLimitRequest> backFlowLimitRequests =
        //                    strategyGroupRequest.getBackFlowLimitRequestList().stream()
        //                            .peek(it -> it.setProjectId(WebUtils.projectId.get()))
        //                            .collect(Collectors.toList());
        //            backFlowLimitService.addBackFlowLimitItems(backFlowLimitRequests);
        //        }
        return Result.success();
    }

    private void changeGirdControlPowerRecord(
            StrategyGroupRequest strategyGroupRequest,
            StrategyEntity oldStrategy,
            GroupEntity groupEntity) {
        DemandLogEntity demandLogEntity = new DemandLogEntity();
        demandLogEntity.setTime(Instant.now().getEpochSecond());
        demandLogEntity.setProjectId(WebUtils.projectId.get());
        demandLogEntity.setGroupId(oldStrategy.getGroupId());
        demandLogEntity.setActualDemand(0d);

        // 意思就是 如果需量控制功率 有变化 就记录一下日志
        if (strategyGroupRequest.getGridControlPower() != null) {
            if (!strategyGroupRequest
                    .getGridControlPower()
                    .equals(oldStrategy.getGridControlPower())) {
                demandLogEntity.setDescription(
                        String.format(
                                "%s当前控制需量调整为%skW",
                                groupEntity.getName(),
                                String.format("%.2f", strategyGroupRequest.getGridControlPower())));
                demandLogEntity.setDescriptionEn(
                        String.format(
                                "The current controlled demand for %s has been adjusted to %s"
                                        + " kW",
                                groupEntity.getName(),
                                String.format("%.2f", strategyGroupRequest.getGridControlPower())));
                demandLogEntity.setType(2);
                demandLogService.save(demandLogEntity);
            }
        }

        // 月初需量调整了 记录一下日志..
        boolean areNotEqual =
                (strategyGroupRequest.getMonthControlPower() != null
                                ^ oldStrategy.getMonthControlPower() != null)
                        || (strategyGroupRequest.getMonthControlPower() != null
                                && !strategyGroupRequest
                                        .getMonthControlPower()
                                        .equals(oldStrategy.getMonthControlPower()));
        if (areNotEqual) {
            demandLogEntity.setId(null);
            demandLogEntity.setDescription(
                    String.format(
                            "%s月初控制需量调整为%skW",
                            groupEntity.getName(), strategyGroupRequest.getMonthControlPower()));
            demandLogEntity.setDescriptionEn(
                    String.format(
                            "The monthly beginning controlled demand for %s has been adjusted"
                                    + " to %s kW.",
                            groupEntity.getName(),
                            String.format("%.2f", strategyGroupRequest.getMonthControlPower())));
            demandLogEntity.setType(3);
            demandLogService.save(demandLogEntity);
        }
    }

    /**
     * 记录一下 容量控制目标变更的 记录
     *
     * @param strategyGroupRequest : request
     * @param oldStrategy : old
     * @param groupEntity : group
     */
    private void changeControlPowerRecord(
            StrategyGroupRequest strategyGroupRequest,
            StrategyEntity oldStrategy,
            GroupEntity groupEntity) {
        if (strategyGroupRequest.getControlPower() != null) {
            if (!strategyGroupRequest.getControlPower().equals(oldStrategy.getControlPower())) {
                CapacityAlarmRecordEntity alarmRecordEntity =
                        new CapacityAlarmRecordEntity()
                                .setProjectId(oldStrategy.getProjectId())
                                .setGroupId(oldStrategy.getGroupId())
                                .setTime(Instant.now().getEpochSecond())
                                .setAlarmValue(0.0)
                                .setControlValue(strategyGroupRequest.getControlPower())
                                .setDescription(
                                        String.format(
                                                "分组名称:%s 并网点控制目标调整为%skVA",
                                                groupEntity.getName(),
                                                String.format(
                                                        "%.2f",
                                                        strategyGroupRequest.getControlPower())))
                                .setDescriptionEn(
                                        String.format(
                                                "The current controlled capacity for %s has been adjusted to %s"
                                                        + " kVA",
                                                groupEntity.getName(),
                                                String.format(
                                                        "%.2f",
                                                        strategyGroupRequest.getControlPower())))
                                .setType(CapacityAlarmTypeEnum.LOG_RECORD.type());
                capacityAlarmRecordService.save(alarmRecordEntity);
            }
        }
    }

    /** 查询策略 */
    @PostMapping("/getStrategy/{groupId}")
    @ApiOperation("查询策略")
    @PreAuthorize("hasAuthority('/strategy/query')")
    @Deprecated
    public Result<Map<Integer, List<StrategyEntity>>> getStrategyByGroupId(
            @PathVariable("groupId") String groupId) {
        Map<Integer, List<StrategyEntity>> map = strategyService.getStrategyByGroupId(groupId);
        return Result.success(map);
    }

    /** 查询策略 */
    @PostMapping("/getStrategyNew/{groupId}")
    @ApiOperation("查询策略")
    @PreAuthorize("hasAuthority('/strategy/query')")
    public Result<Map<String, List<StrategyEntity>>> getStrategyByGroupIdNew(
            @PathVariable("groupId") String groupId,
            @RequestParam(value = "dateStr", required = false) String dateStr) {
        Map<String, List<StrategyEntity>> map =
                strategyServiceKt.getStrategyByGroupIdNew(
                        WebUtils.projectId.get(), groupId, dateStr);
        return Result.success(map);
    }

    @PostMapping("/getSystemStrategyNew/{groupId}")
    @ApiOperation("查询策略")
    @PreAuthorize("hasAuthority('/strategy/query')")
    public Result<Map<String, StrategyEntity>> getSystemStrategyNew(
            @PathVariable("groupId") String groupId) {
        Map<String, StrategyEntity> map =
                strategyServiceKt.getStrategySystemByGroupIdNew(WebUtils.projectId.get(), groupId);
        return Result.success(map);
    }

    /** 查询策略 */
    @PostMapping("/getRunStrategy/{groupId}")
    @ApiOperation("查询实际运行策略")
    @PreAuthorize("hasAuthority('/strategy/query')")
    @Deprecated
    public Result<GroupGoRequest> getRunStrategyByGroupId(@PathVariable("groupId") String groupId) {
        ProjectEntity projectEntity = projectService.getById(WebUtils.projectId.get());
        GroupGoRequest groupGoRequest = strategyService.getGroupGoRequest(projectEntity, groupId);
        List<Strategy> strategies = groupGoRequest.getStrategies();
        strategies = StrategyUtil.fillStrategies(strategies, projectEntity.getTimezone());
        groupGoRequest.setStrategies(strategies);
        return Result.success(groupGoRequest);
    }

    //    /** 老的策略方式 */
    //    /** 查询策略 */
    //    @PostMapping("/getPredicateRunStrategy")
    //    @ApiOperation("查询预览策略")
    //    @PreAuthorize("hasAuthority('/strategy/query')")
    //    @Deprecated
    //    public Result<List<Strategy>> getPredicateRunStrategy(
    //            @RequestBody PredicateStrategyRequest predicateStrategyRequest) {
    //        //        ProjectEntity projectEntity =
    // projectService.getById(WebUtils.projectId.get());
    //        //        List<Strategy> strategies =
    //        //                strategyService.getPredicateStrategy(
    //        //                        predicateStrategyRequest.getGroupId(),
    //        //                        predicateStrategyRequest.getStrategyType());
    //        //        // 只预览今天的策略
    //        //        long start = MyTimeUtil.getTodayZeroTime(projectEntity.getTimezone());
    //        //        int weekday = MyTimeUtil.getWeekday(start, projectEntity.getTimezone());
    //        //        strategies =
    //        //                strategies.stream()
    //        //                        .filter(strategy -> strategy.getWeek() == weekday)
    //        //                        .collect(Collectors.toList());
    //        //        strategies = StrategyUtil.fillStrategies(strategies,
    // projectEntity.getTimezone());
    //        //        return Result.success(strategies);
    //    }

    @PostMapping("/getPredicateRunStrategyNewForTemplate")
    @ApiOperation("查询预览策略Newfor template ")
    @PreAuthorize("hasAuthority('/strategy/query')")
    public Result<List<YearlyStrategy>> getPredicateRunStrategyNewForTemplate(
            @RequestBody PredicateStrategyTemplateRequest predicateStrategyTemplateRequest) {
        ProjectEntity project = projectService.getById(WebUtils.projectId.get());
        // 前端可以根据 时间去预览对应天的 策略
        String todayStrategyDateStr =
                StrategyCommonUtils.Companion.getStrategyDateStr(
                        project,
                        predicateStrategyTemplateRequest.getDate() == null
                                ? MyTimeUtil.getTodayZeroTime(project.getTimezone())
                                : predicateStrategyTemplateRequest.getDate());
        Map<String, List<YearlyStrategy>> stringListMap =
                defaultCustomModelStrategy.executeModelPredicate(
                        new PredicateContext(
                                project,
                                null,
                                predicateStrategyTemplateRequest.getDate(),
                                new TemplatePreviewContext(
                                        predicateStrategyTemplateRequest.getTemplateId())));
        List<YearlyStrategy> yearlyStrategies = stringListMap.get(todayStrategyDateStr);
        return Result.success(yearlyStrategies);
    }

    @PostMapping("/getPredicateRunStrategyNew")
    @ApiOperation("查询预览策略New")
    @PreAuthorize("hasAuthority('/strategy/query')")
    public Result<List<YearlyStrategy>> getPredicateRunStrategyNew(
            @RequestBody PredicateStrategyRequest predicateStrategyRequest) {
        ProjectEntity project = projectService.getById(WebUtils.projectId.get());
        // 只预览今天的策略
        GroupEntity group = groupService.getById(predicateStrategyRequest.getGroupId());
        if (predicateStrategyRequest.getDate() != null) {
            if (predicateStrategyRequest.getDate()
                    > MyTimeUtil.getTodayZeroTime(project.getTimezone())
                            * EmsConstants.ONE_DAY_SECOND) {
                return Result.success(new ArrayList<>());
            }
        }
        String todayStrategyDateStr =
                StrategyCommonUtils.Companion.getStrategyDateStr(
                        project,
                        predicateStrategyRequest.getDate() == null
                                ? MyTimeUtil.getTodayZeroTime(project.getTimezone())
                                : predicateStrategyRequest.getDate());
        Map<String, List<YearlyStrategy>> stringListMap =
                defaultCustomModelStrategy.executeModelPredicate(
                        new PredicateContext(
                                project, group, predicateStrategyRequest.getDate(), null));
        List<YearlyStrategy> yearlyStrategies = stringListMap.get(todayStrategyDateStr);
        return Result.success(yearlyStrategies);
    }

    /** 查询系统策略 */
    @PostMapping("/getSystemStrategy")
    @ApiOperation("查询系统策略")
    @PreAuthorize("hasAuthority('/strategy/query')")
    @Deprecated
    public Result<Map<Integer, List<StrategyEntity>>> getSystemStrategy() {
        GroupEntity systemGroupEntity =
                groupService.getOne(
                        Wrappers.lambdaQuery(GroupEntity.class)
                                .eq(GroupEntity::getProjectId, WebUtils.projectId.get())
                                .eq(GroupEntity::getWhetherSystem, true));
        Map<Integer, List<StrategyEntity>> map =
                strategyService.getStrategyByGroupId(systemGroupEntity.getId());
        return Result.success(map);
    }

    /** 导入时间策略 */
    @PostMapping("/importTimeStrategy")
    @ApiOperation("导入时间策略")
    @Log(module = "STRATEGY", methods = "STRATEGY_TIME_IMPORT", type = OperationType.ADD_SIMPLE)
    @PreAuthorize("hasAuthority('/strategy/time/import')")
    public Result<Object> importTimeStrategy(
            @RequestBody ImportStrategyRequest importStrategyRequest) {
        if (importStrategyRequest.getFromWeekDay() <= 0
                || importStrategyRequest.getFromWeekDay() > EmsConstants.DAY_NUM_OF_WEEK) {
            throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
        }
        for (Integer toWeekDay : importStrategyRequest.getToWeekDays()) {
            if (toWeekDay <= 0 || toWeekDay > EmsConstants.DAY_NUM_OF_WEEK) {
                throw new ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value());
            }
        }
        strategyService.importTimeStrategy(importStrategyRequest);
        return Result.success();
    }

    /** 导入整个分组策略 */
    @PostMapping("/importGroupStrategy")
    @ApiOperation("导入分组策略")
    @PreAuthorize("hasAuthority('/strategy/import')")
    @Log(module = "STRATEGY", methods = "STRATEGY_GROUP_IMPORT", type = OperationType.ADD_SIMPLE)
    public Result<Object> importGroupStrategy(@RequestBody ImportGroupRequest importGroupRequest) {
        strategyService.importGroupStrategy(importGroupRequest);
        return Result.success();
    }

    /** 导入整个分组策略 */
    @PostMapping("/importGroupStrategyNew")
    @ApiOperation("导入分组策略New")
    @PreAuthorize("hasAuthority('/strategy/import')")
    @Log(module = "STRATEGY", methods = "STRATEGY_GROUP_IMPORT", type = OperationType.ADD_SIMPLE)
    public Result<Object> importGroupStrategyNew(
            @RequestBody ImportGroupRequest importGroupRequest) {
        // strategyService.importGroupStrategy(importGroupRequest);
        strategyImportService.importGroupStrategy(importGroupRequest);
        return Result.success();
    }

    /** 分组策略下发 */
    @PostMapping("/uploadStrategy")
    @ApiOperation("分组策略下发")
    @Log(module = "STRATEGY", methods = "STRATEGY_UPLOAD")
    @PreAuthorize("hasAuthority('/strategy/upload')")
    public Result<Object> uploadStrategy() {
        boolean uploaded = strategyService.uploadStrategy(WebUtils.projectId.get());
        if (!uploaded) {
            throw new ServiceException(ErrorResultCode.FAIL_CONNECT_GOCONTROL.value());
        }
        return Result.success();
    }

    /** check time over */
    public void checkTime(ProjectEntity projectEntity, StrategyTimeRequest strategyTimeRequest) {
        String strategyDate = strategyTimeRequest.getStrategyDate();
        String electricPriceType = projectEntity.getElectricPriceType();

        List<StrategyEntity> list = new ArrayList<>();
        if (electricPriceType.equals(ElectricPriceTypeEnum.PEAK_VALLEYS_PRICE_PERIOD.name())
                || electricPriceType.equals(ElectricPriceTypeEnum.FIXED_PRICE_PERIOD.name())) {
            list =
                    strategyService.list(
                            Wrappers.lambdaQuery(StrategyEntity.class)
                                    .eq(StrategyEntity::getProjectId, WebUtils.projectId.get())
                                    .eq(
                                            StrategyEntity::getGroupId,
                                            strategyTimeRequest.getGroupId())
                                    .eq(StrategyEntity::getStrategyDate, strategyDate)
                                    .ne(
                                            strategyTimeRequest.getId() != null,
                                            StrategyEntity::getId,
                                            strategyTimeRequest.getId())
                                    .orderByAsc(StrategyEntity::getStartTime));
        } else if (electricPriceType.equals(ElectricPriceTypeEnum.DYNAMIC_PRICE_PERIOD.name())) {
            Integer type = strategyTimeRequest.getType();
            if (type != 2) {
                list =
                        strategyService.list(
                                Wrappers.lambdaQuery(StrategyEntity.class)
                                        .eq(StrategyEntity::getProjectId, WebUtils.projectId.get())
                                        .eq(
                                                StrategyEntity::getGroupId,
                                                strategyTimeRequest.getGroupId())
                                        .eq(StrategyEntity::getStrategyDate, strategyDate)
                                        .ne(
                                                strategyTimeRequest.getId() != null,
                                                StrategyEntity::getId,
                                                strategyTimeRequest.getId())
                                        .eq(StrategyEntity::getType, strategyTimeRequest.getType())
                                        .orderByAsc(StrategyEntity::getStartTime));
            }
            if (type == 2) {
                list =
                        strategyService.list(
                                Wrappers.lambdaQuery(StrategyEntity.class)
                                        .eq(StrategyEntity::getProjectId, WebUtils.projectId.get())
                                        .eq(
                                                StrategyEntity::getGroupId,
                                                strategyTimeRequest.getGroupId())
                                        .eq(StrategyEntity::getStrategyDate, strategyDate)
                                        .ne(
                                                strategyTimeRequest.getId() != null,
                                                StrategyEntity::getId,
                                                strategyTimeRequest.getId())
                                        .orderByAsc(StrategyEntity::getStartTime));
            }
        }
        if (strategyTimeRequest.getEndTime().toSecondOfDay() != 0
                && !strategyTimeRequest.getEndTime().isAfter(strategyTimeRequest.getStartTime())) {
            throw new ServiceException(ErrorResultCode.START_GE_END_TIME.value());
        }

        // 不需要判断size=0，如果等于0，直接插入数据
        if (list == null || list.isEmpty()) {
            return;
        }
        if (strategyTimeRequest.getEndTime().toSecondOfDay() == 0) {
            if (list.get(list.size() - 1).getEndTime().toSecondOfDay() == 0) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
            if (strategyTimeRequest
                    .getStartTime()
                    .isBefore(list.get(list.size() - 1).getEndTime())) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }
        for (StrategyEntity strategyEntity : list) {
            // if update ignore itself
            if (Objects.equals(strategyTimeRequest.getId(), strategyEntity.getId())) {
                continue;
            }
            if (!strategyTimeRequest.getEndTime().isAfter(strategyEntity.getStartTime())) {
                break;
            }
            if (strategyTimeRequest.getStartTime().isBefore(strategyEntity.getEndTime())
                    || strategyEntity.getEndTime().toSecondOfDay() == 0) {
                throw new ServiceException(ErrorResultCode.TIME_OVERLAPPED.value());
            }
        }
    }
}
