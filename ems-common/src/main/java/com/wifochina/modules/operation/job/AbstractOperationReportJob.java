package com.wifochina.modules.operation.job;

import java.util.List;
import java.util.stream.Collectors;

import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import com.wifochina.modules.metrics.OperationReportJobMetrics;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-03-13 3:05 PM
 */
@Slf4j
public abstract class AbstractOperationReportJob extends QuartzJobBean {
    @Override
    protected void executeInternal(JobExecutionContext context) {
        ProjectService projectService = SpringUtil.getBean(ProjectService.class);
        OperationReportJobMetrics metrics = SpringUtil.getBean(OperationReportJobMetrics.class);
        String timezoneCode = (String)context.getJobDetail().getJobDataMap().get("timezoneCode");
        List<ProjectEntity> projectEntities = getProjectEntities(projectService, timezoneCode);
        long end = getEnd(timezoneCode);
        String jobType = determineJobType();

        for (ProjectEntity projectEntity : projectEntities) {
            if (checkTime(projectEntity.getCreateTime(), end)) {
                continue;
            }

            String projectId = projectEntity.getId();
            String projectName = projectEntity.getProjectName();

            // 开始计时
            io.micrometer.core.instrument.Timer.Sample sample =
                metrics.startTimer(projectId, projectName, jobType, timezoneCode);

            try {
                log.info("{} 发送收益报告开始", projectName);
                sendReport(projectId, timezoneCode);
                log.info("{} 发送收益报告完成", projectName);

                // 记录成功指标
                metrics.recordSuccess(projectId, projectName, jobType, timezoneCode);

            } catch (Exception e) {
                log.error("{} 发送收益报告失败", projectName, e);

                // 记录失败指标
                String errorMessage = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
                metrics.recordFailure(projectId, projectName, jobType, timezoneCode, errorMessage);

            } finally {
                // 停止计时
                metrics.stopTimer(sample, projectId, jobType);
            }
        }
    }

    public abstract Long getEnd(String timezoneCode);

    public abstract void sendReport(String projectId, String timezoneCode);

    public abstract Long getStart(String timezoneCode);

    /**
     * 根据当前类名确定任务类型
     *
     * @return 任务类型（month/year）
     */
    protected String determineJobType() {
        String className = this.getClass().getSimpleName();
        if (className.contains("Month")) {
            return "month";
        } else if (className.contains("Year")) {
            return "year";
        } else {
            return "unknown";
        }
    }

    private static boolean checkTime(Long time, long end) {
        return time >= end;
    }

    @NotNull
    private static List<ProjectEntity> getProjectEntities(ProjectService projectService, String timezoneCode) {
        List<ProjectEntity> projectEntities =
            projectService.lambdaQuery().eq(ProjectEntity::getWhetherDelete, false).list();
        if (projectEntities.size() > 1) {
            // 云项目
            projectEntities = projectEntities.stream().filter(projectEntity -> projectEntity.getProjectModel() != 1)
                .filter(projectEntity -> timezoneCode.equals(projectEntity.getTimezone())).collect(Collectors.toList());
        }
        return projectEntities;
    }
}
