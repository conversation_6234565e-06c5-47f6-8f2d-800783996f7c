# Operation Report Job Prometheus 指标

本模块为运营报告任务（Operation Report Job）提供了完整的Prometheus指标收集功能，用于监控任务执行情况。

## 功能特性

- **自动指标收集**：通过AOP切面自动收集任务执行指标
- **多维度标签**：支持项目ID、项目名称、任务类型、时区等标签
- **执行时间监控**：记录任务执行耗时
- **成功/失败统计**：分别统计成功和失败的执行次数
- **REST API**：提供指标查询和管理接口

## 指标说明

### 1. 成功执行计数器
- **指标名称**：`operation_report_job_success_total`
- **描述**：运营报告任务执行成功总数
- **标签**：
  - `project_id`：项目ID
  - `project_name`：项目名称
  - `job_type`：任务类型（month/year）
  - `timezone`：时区代码
  - `occurrence_time`：发生时间戳

### 2. 失败执行计数器
- **指标名称**：`operation_report_job_failure_total`
- **描述**：运营报告任务执行失败总数
- **标签**：
  - `project_id`：项目ID
  - `project_name`：项目名称
  - `job_type`：任务类型（month/year）
  - `timezone`：时区代码
  - `error_message`：错误信息（截断至100字符）
  - `occurrence_time`：发生时间戳

### 3. 执行时间计时器
- **指标名称**：`operation_report_job_execution_duration_seconds`
- **描述**：运营报告任务执行耗时（秒）
- **标签**：
  - `project_id`：项目ID
  - `project_name`：项目名称
  - `job_type`：任务类型（month/year）
  - `timezone`：时区代码

## 使用方式

### 1. 自动收集（推荐）
指标收集已经集成到`AbstractOperationReportJob`中，所有继承该类的任务都会自动收集指标，无需额外配置。

### 2. 手动收集
如果需要在其他地方手动收集指标，可以注入`OperationReportJobMetrics`：

```java
@Autowired
private OperationReportJobMetrics metrics;

// 记录成功
metrics.recordSuccess(projectId, projectName, jobType, timezoneCode);

// 记录失败
metrics.recordFailure(projectId, projectName, jobType, timezoneCode, errorMessage);

// 计时
Timer.Sample sample = metrics.startTimer(projectId, projectName, jobType, timezoneCode);
// ... 执行任务 ...
metrics.stopTimer(sample, projectId, jobType);
```

## REST API

### 1. 获取项目指标
```
GET /api/metrics/operation-report-job/project/{projectId}?jobType=month
```

### 2. 获取指标概览
```
GET /api/metrics/operation-report-job/summary
```

### 3. 清理项目指标缓存
```
DELETE /api/metrics/operation-report-job/project/{projectId}?jobType=month
```

### 4. 测试接口
```
POST /api/metrics/operation-report-job/test/success
POST /api/metrics/operation-report-job/test/failure
```

## Prometheus查询示例

### 1. 查看成功率
```promql
# 按项目查看成功率
rate(operation_report_job_success_total[5m]) / 
(rate(operation_report_job_success_total[5m]) + rate(operation_report_job_failure_total[5m]))

# 按任务类型查看成功率
sum(rate(operation_report_job_success_total[5m])) by (job_type) / 
(sum(rate(operation_report_job_success_total[5m])) by (job_type) + sum(rate(operation_report_job_failure_total[5m])) by (job_type))
```

### 2. 查看执行时间
```promql
# 平均执行时间
rate(operation_report_job_execution_duration_seconds_sum[5m]) / 
rate(operation_report_job_execution_duration_seconds_count[5m])

# 95分位数执行时间
histogram_quantile(0.95, rate(operation_report_job_execution_duration_seconds_bucket[5m]))
```

### 3. 查看失败情况
```promql
# 失败率
rate(operation_report_job_failure_total[5m])

# 按错误类型分组的失败次数
sum(rate(operation_report_job_failure_total[5m])) by (error_message)
```

## 告警规则示例

```yaml
groups:
  - name: operation_report_job
    rules:
      - alert: OperationReportJobHighFailureRate
        expr: |
          (
            rate(operation_report_job_failure_total[5m]) / 
            (rate(operation_report_job_success_total[5m]) + rate(operation_report_job_failure_total[5m]))
          ) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "运营报告任务失败率过高"
          description: "项目 {{ $labels.project_name }} 的 {{ $labels.job_type }} 任务失败率超过10%"

      - alert: OperationReportJobSlowExecution
        expr: |
          histogram_quantile(0.95, rate(operation_report_job_execution_duration_seconds_bucket[5m])) > 300
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "运营报告任务执行缓慢"
          description: "项目 {{ $labels.project_name }} 的 {{ $labels.job_type }} 任务95分位数执行时间超过5分钟"
```

## 注意事项

1. **内存使用**：指标会在内存中缓存，对于大量项目可能会占用较多内存
2. **标签基数**：避免使用高基数标签（如时间戳），以免影响Prometheus性能
3. **错误信息截断**：错误信息会被截断至100字符，避免标签值过长
4. **清理机制**：可以通过REST API手动清理不需要的指标缓存

## 依赖要求

- Spring Boot Actuator
- Micrometer
- AspectJ（用于AOP切面）

这些依赖在当前项目中已经包含，无需额外添加。
