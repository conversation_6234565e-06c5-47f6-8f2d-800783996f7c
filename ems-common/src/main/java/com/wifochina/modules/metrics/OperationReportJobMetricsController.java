package com.wifochina.modules.metrics;

import com.wifochina.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Operation Report Job指标查询控制器
 * 提供指标查询和管理接口
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@RestController
@RequestMapping("/api/metrics/operation-report-job")
@Tag(name = "运营报告任务指标", description = "运营报告任务Prometheus指标管理")
@Slf4j
public class OperationReportJobMetricsController {

    @Autowired
    private OperationReportJobMetrics operationReportJobMetrics;

    /**
     * 获取指定项目的指标统计
     * 
     * @param projectId 项目ID
     * @param jobType 任务类型（可选，不传则返回所有类型）
     * @return 指标统计结果
     */
    @GetMapping("/project/{projectId}")
    @Operation(summary = "获取项目指标统计", description = "获取指定项目的运营报告任务执行指标")
    public Result<Map<String, Object>> getProjectMetrics(
            @Parameter(description = "项目ID", required = true) @PathVariable String projectId,
            @Parameter(description = "任务类型（month/year）") @RequestParam(required = false) String jobType) {
        
        Map<String, Object> result = new HashMap<>();
        
        if (jobType != null) {
            // 获取指定类型的指标
            double successCount = operationReportJobMetrics.getSuccessCount(projectId, jobType);
            double failureCount = operationReportJobMetrics.getFailureCount(projectId, jobType);
            
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("success_count", successCount);
            metrics.put("failure_count", failureCount);
            metrics.put("total_count", successCount + failureCount);
            metrics.put("success_rate", (successCount + failureCount) > 0 ? 
                       successCount / (successCount + failureCount) : 0.0);
            
            result.put(jobType, metrics);
        } else {
            // 获取所有类型的指标
            String[] jobTypes = {"month", "year"};
            for (String type : jobTypes) {
                double successCount = operationReportJobMetrics.getSuccessCount(projectId, type);
                double failureCount = operationReportJobMetrics.getFailureCount(projectId, type);
                
                Map<String, Object> metrics = new HashMap<>();
                metrics.put("success_count", successCount);
                metrics.put("failure_count", failureCount);
                metrics.put("total_count", successCount + failureCount);
                metrics.put("success_rate", (successCount + failureCount) > 0 ? 
                           successCount / (successCount + failureCount) : 0.0);
                
                result.put(type, metrics);
            }
        }
        
        result.put("project_id", projectId);
        return Result.success(result);
    }

    /**
     * 获取所有指标的概览信息
     * 
     * @return 指标概览
     */
    @GetMapping("/summary")
    @Operation(summary = "获取指标概览", description = "获取所有运营报告任务指标的概览信息")
    public Result<String> getMetricsSummary() {
        String summary = operationReportJobMetrics.getMetricsSummary();
        return Result.success(summary);
    }

    /**
     * 清理指定项目的指标缓存
     * 
     * @param projectId 项目ID
     * @param jobType 任务类型
     * @return 操作结果
     */
    @DeleteMapping("/project/{projectId}")
    @Operation(summary = "清理项目指标", description = "清理指定项目的运营报告任务指标缓存")
    public Result<String> clearProjectMetrics(
            @Parameter(description = "项目ID", required = true) @PathVariable String projectId,
            @Parameter(description = "任务类型（month/year）", required = true) @RequestParam String jobType) {
        
        try {
            operationReportJobMetrics.clearMetrics(projectId, jobType);
            log.info("Cleared metrics for project: {}, jobType: {}", projectId, jobType);
            return Result.success("指标缓存清理成功");
        } catch (Exception e) {
            log.error("Failed to clear metrics for project: {}, jobType: {}", projectId, jobType, e);
            return Result.error("指标缓存清理失败: " + e.getMessage());
        }
    }

    /**
     * 手动记录成功指标（用于测试）
     * 
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @param jobType 任务类型
     * @param timezoneCode 时区代码
     * @return 操作结果
     */
    @PostMapping("/test/success")
    @Operation(summary = "测试记录成功指标", description = "手动记录一次成功指标（仅用于测试）")
    public Result<String> recordTestSuccess(
            @Parameter(description = "项目ID", required = true) @RequestParam String projectId,
            @Parameter(description = "项目名称", required = true) @RequestParam String projectName,
            @Parameter(description = "任务类型（month/year）", required = true) @RequestParam String jobType,
            @Parameter(description = "时区代码", required = true) @RequestParam String timezoneCode) {
        
        try {
            operationReportJobMetrics.recordSuccess(projectId, projectName, jobType, timezoneCode);
            log.info("Test success metric recorded for project: {} ({}), type: {}", 
                    projectName, projectId, jobType);
            return Result.success("测试成功指标记录完成");
        } catch (Exception e) {
            log.error("Failed to record test success metric", e);
            return Result.error("测试成功指标记录失败: " + e.getMessage());
        }
    }

    /**
     * 手动记录失败指标（用于测试）
     * 
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @param jobType 任务类型
     * @param timezoneCode 时区代码
     * @param errorMessage 错误信息
     * @return 操作结果
     */
    @PostMapping("/test/failure")
    @Operation(summary = "测试记录失败指标", description = "手动记录一次失败指标（仅用于测试）")
    public Result<String> recordTestFailure(
            @Parameter(description = "项目ID", required = true) @RequestParam String projectId,
            @Parameter(description = "项目名称", required = true) @RequestParam String projectName,
            @Parameter(description = "任务类型（month/year）", required = true) @RequestParam String jobType,
            @Parameter(description = "时区代码", required = true) @RequestParam String timezoneCode,
            @Parameter(description = "错误信息") @RequestParam(required = false, defaultValue = "test error") String errorMessage) {
        
        try {
            operationReportJobMetrics.recordFailure(projectId, projectName, jobType, timezoneCode, errorMessage);
            log.info("Test failure metric recorded for project: {} ({}), type: {}, error: {}", 
                    projectName, projectId, jobType, errorMessage);
            return Result.success("测试失败指标记录完成");
        } catch (Exception e) {
            log.error("Failed to record test failure metric", e);
            return Result.error("测试失败指标记录失败: " + e.getMessage());
        }
    }
}
