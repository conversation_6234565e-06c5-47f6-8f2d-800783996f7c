package com.wifochina.modules.metrics;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 指标收集AOP配置类
 * 启用AspectJ自动代理，确保指标收集切面能够正常工作
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Configuration
@EnableAspectJAutoProxy
public class MetricsAspectConfiguration {
    // 该配置类主要用于启用AOP功能
    // 确保OperationReportJobMetricsAspect切面能够正常拦截方法调用
}
