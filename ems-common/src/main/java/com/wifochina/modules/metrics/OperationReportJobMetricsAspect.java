package com.wifochina.modules.metrics;

import com.wifochina.modules.project.entity.ProjectEntity;
import com.wifochina.modules.project.service.ProjectService;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Operation Report Job指标收集切面
 * 通过AOP自动收集运营报告任务的执行指标
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Aspect
@Component
@Slf4j
public class OperationReportJobMetricsAspect {

    @Autowired
    private OperationReportJobMetrics operationReportJobMetrics;
    
    @Autowired
    private ProjectService projectService;

    /**
     * 拦截sendReport方法，收集执行指标
     * 
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("execution(* com.wifochina.modules.operation.job.AbstractOperationReportJob.sendReport(..))")
    public Object aroundSendReport(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (args.length < 2) {
            return joinPoint.proceed();
        }
        
        String projectId = (String) args[0];
        String timezoneCode = (String) args[1];
        
        // 获取项目信息
        ProjectEntity projectEntity = projectService.getById(projectId);
        if (projectEntity == null) {
            log.warn("Project not found for id: {}", projectId);
            return joinPoint.proceed();
        }
        
        String projectName = projectEntity.getProjectName();
        String jobType = determineJobType(joinPoint.getTarget().getClass().getSimpleName());
        
        // 开始计时
        Timer.Sample sample = operationReportJobMetrics.startTimer(projectId, projectName, jobType, timezoneCode);
        
        try {
            // 执行原方法
            Object result = joinPoint.proceed();
            
            // 记录成功
            operationReportJobMetrics.recordSuccess(projectId, projectName, jobType, timezoneCode);
            log.debug("Operation report job success recorded for project: {} ({}), type: {}", 
                     projectName, projectId, jobType);
            
            return result;
            
        } catch (Exception e) {
            // 记录失败
            String errorMessage = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            operationReportJobMetrics.recordFailure(projectId, projectName, jobType, timezoneCode, errorMessage);
            log.error("Operation report job failure recorded for project: {} ({}), type: {}, error: {}", 
                     projectName, projectId, jobType, errorMessage);
            
            // 重新抛出异常
            throw e;
            
        } finally {
            // 停止计时
            operationReportJobMetrics.stopTimer(sample, projectId, jobType);
        }
    }

    /**
     * 根据类名确定任务类型
     * 
     * @param className 类名
     * @return 任务类型
     */
    private String determineJobType(String className) {
        if (className.contains("Month")) {
            return "month";
        } else if (className.contains("Year")) {
            return "year";
        } else {
            return "unknown";
        }
    }
}
