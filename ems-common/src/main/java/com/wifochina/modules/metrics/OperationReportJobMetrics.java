package com.wifochina.modules.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Operation Report Job Prometheus指标收集器
 * 用于监控运营报告任务的执行情况
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class OperationReportJobMetrics {

    private final MeterRegistry registry;
    
    // 存储各个项目和任务类型的成功计数器
    private final Map<String, Counter> successCounters = new ConcurrentHashMap<>();
    
    // 存储各个项目和任务类型的失败计数器
    private final Map<String, Counter> failureCounters = new ConcurrentHashMap<>();
    
    // 存储各个项目和任务类型的执行时间计时器
    private final Map<String, Timer> executionTimers = new ConcurrentHashMap<>();

    public OperationReportJobMetrics(MeterRegistry registry) {
        this.registry = registry;
    }

    /**
     * 记录任务执行成功
     * 
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @param jobType 任务类型（month/year）
     * @param timezoneCode 时区代码
     */
    public void recordSuccess(String projectId, String projectName, String jobType, String timezoneCode) {
        String key = generateKey(projectId, jobType);
        Counter counter = successCounters.computeIfAbsent(key, k -> 
            Counter.builder("operation_report_job_success_total")
                    .description("运营报告任务执行成功总数")
                    .tag("project_id", projectId)
                    .tag("project_name", projectName)
                    .tag("job_type", jobType)
                    .tag("timezone", timezoneCode)
                    .tag("occurrence_time", String.valueOf(Instant.now().getEpochSecond()))
                    .register(registry)
        );
        counter.increment();
    }

    /**
     * 记录任务执行失败
     * 
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @param jobType 任务类型（month/year）
     * @param timezoneCode 时区代码
     * @param errorMessage 错误信息
     */
    public void recordFailure(String projectId, String projectName, String jobType, String timezoneCode, String errorMessage) {
        String key = generateKey(projectId, jobType);
        Counter counter = failureCounters.computeIfAbsent(key, k -> 
            Counter.builder("operation_report_job_failure_total")
                    .description("运营报告任务执行失败总数")
                    .tag("project_id", projectId)
                    .tag("project_name", projectName)
                    .tag("job_type", jobType)
                    .tag("timezone", timezoneCode)
                    .tag("error_message", truncateErrorMessage(errorMessage))
                    .tag("occurrence_time", String.valueOf(Instant.now().getEpochSecond()))
                    .register(registry)
        );
        counter.increment();
    }

    /**
     * 开始计时任务执行时间
     * 
     * @param projectId 项目ID
     * @param projectName 项目名称
     * @param jobType 任务类型（month/year）
     * @param timezoneCode 时区代码
     * @return Timer.Sample 计时器样本
     */
    public Timer.Sample startTimer(String projectId, String projectName, String jobType, String timezoneCode) {
        String key = generateKey(projectId, jobType);
        Timer timer = executionTimers.computeIfAbsent(key, k -> 
            Timer.builder("operation_report_job_execution_duration_seconds")
                    .description("运营报告任务执行耗时（秒）")
                    .tag("project_id", projectId)
                    .tag("project_name", projectName)
                    .tag("job_type", jobType)
                    .tag("timezone", timezoneCode)
                    .register(registry)
        );
        return Timer.start(registry);
    }

    /**
     * 停止计时并记录执行时间
     * 
     * @param sample 计时器样本
     * @param projectId 项目ID
     * @param jobType 任务类型
     */
    public void stopTimer(Timer.Sample sample, String projectId, String jobType) {
        String key = generateKey(projectId, jobType);
        Timer timer = executionTimers.get(key);
        if (timer != null && sample != null) {
            sample.stop(timer);
        }
    }

    /**
     * 获取项目成功执行次数
     * 
     * @param projectId 项目ID
     * @param jobType 任务类型
     * @return 成功执行次数
     */
    public double getSuccessCount(String projectId, String jobType) {
        String key = generateKey(projectId, jobType);
        Counter counter = successCounters.get(key);
        return counter != null ? counter.count() : 0.0;
    }

    /**
     * 获取项目失败执行次数
     * 
     * @param projectId 项目ID
     * @param jobType 任务类型
     * @return 失败执行次数
     */
    public double getFailureCount(String projectId, String jobType) {
        String key = generateKey(projectId, jobType);
        Counter counter = failureCounters.get(key);
        return counter != null ? counter.count() : 0.0;
    }

    /**
     * 生成缓存键
     * 
     * @param projectId 项目ID
     * @param jobType 任务类型
     * @return 缓存键
     */
    private String generateKey(String projectId, String jobType) {
        return projectId + ":" + jobType;
    }

    /**
     * 截断错误信息，避免标签值过长
     * 
     * @param errorMessage 原始错误信息
     * @return 截断后的错误信息
     */
    private String truncateErrorMessage(String errorMessage) {
        if (errorMessage == null) {
            return "unknown";
        }
        // 限制错误信息长度为100个字符
        return errorMessage.length() > 100 ? errorMessage.substring(0, 100) + "..." : errorMessage;
    }

    /**
     * 清理指定项目的指标缓存
     * 
     * @param projectId 项目ID
     * @param jobType 任务类型
     */
    public void clearMetrics(String projectId, String jobType) {
        String key = generateKey(projectId, jobType);
        successCounters.remove(key);
        failureCounters.remove(key);
        executionTimers.remove(key);
    }

    /**
     * 获取所有已注册的项目指标统计
     * 
     * @return 指标统计信息
     */
    public String getMetricsSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Operation Report Job Metrics Summary:\n");
        summary.append("Success Counters: ").append(successCounters.size()).append("\n");
        summary.append("Failure Counters: ").append(failureCounters.size()).append("\n");
        summary.append("Execution Timers: ").append(executionTimers.size()).append("\n");
        return summary.toString();
    }
}
