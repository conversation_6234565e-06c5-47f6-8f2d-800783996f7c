package com.wifochina.modules.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Operation Report Job指标测试类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
class OperationReportJobMetricsTest {

    private OperationReportJobMetrics metrics;
    private MeterRegistry meterRegistry;

    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        metrics = new OperationReportJobMetrics(meterRegistry);
    }

    @Test
    void testRecordSuccess() {
        // Given
        String projectId = "test-project-1";
        String projectName = "测试项目1";
        String jobType = "month";
        String timezoneCode = "Asia/Shanghai";

        // When
        metrics.recordSuccess(projectId, projectName, jobType, timezoneCode);

        // Then
        double successCount = metrics.getSuccessCount(projectId, jobType);
        assertEquals(1.0, successCount);
        
        double failureCount = metrics.getFailureCount(projectId, jobType);
        assertEquals(0.0, failureCount);
    }

    @Test
    void testRecordFailure() {
        // Given
        String projectId = "test-project-2";
        String projectName = "测试项目2";
        String jobType = "year";
        String timezoneCode = "Asia/Shanghai";
        String errorMessage = "测试错误信息";

        // When
        metrics.recordFailure(projectId, projectName, jobType, timezoneCode, errorMessage);

        // Then
        double failureCount = metrics.getFailureCount(projectId, jobType);
        assertEquals(1.0, failureCount);
        
        double successCount = metrics.getSuccessCount(projectId, jobType);
        assertEquals(0.0, successCount);
    }

    @Test
    void testMultipleRecords() {
        // Given
        String projectId = "test-project-3";
        String projectName = "测试项目3";
        String jobType = "month";
        String timezoneCode = "Asia/Shanghai";

        // When - 记录多次成功和失败
        metrics.recordSuccess(projectId, projectName, jobType, timezoneCode);
        metrics.recordSuccess(projectId, projectName, jobType, timezoneCode);
        metrics.recordFailure(projectId, projectName, jobType, timezoneCode, "错误1");

        // Then
        double successCount = metrics.getSuccessCount(projectId, jobType);
        assertEquals(2.0, successCount);
        
        double failureCount = metrics.getFailureCount(projectId, jobType);
        assertEquals(1.0, failureCount);
    }

    @Test
    void testDifferentJobTypes() {
        // Given
        String projectId = "test-project-4";
        String projectName = "测试项目4";
        String timezoneCode = "Asia/Shanghai";

        // When - 记录不同任务类型的指标
        metrics.recordSuccess(projectId, projectName, "month", timezoneCode);
        metrics.recordSuccess(projectId, projectName, "year", timezoneCode);
        metrics.recordFailure(projectId, projectName, "month", timezoneCode, "月报错误");

        // Then
        assertEquals(1.0, metrics.getSuccessCount(projectId, "month"));
        assertEquals(1.0, metrics.getFailureCount(projectId, "month"));
        assertEquals(1.0, metrics.getSuccessCount(projectId, "year"));
        assertEquals(0.0, metrics.getFailureCount(projectId, "year"));
    }

    @Test
    void testTimer() {
        // Given
        String projectId = "test-project-5";
        String projectName = "测试项目5";
        String jobType = "month";
        String timezoneCode = "Asia/Shanghai";

        // When
        var sample = metrics.startTimer(projectId, projectName, jobType, timezoneCode);
        assertNotNull(sample);
        
        // 模拟一些处理时间
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        metrics.stopTimer(sample, projectId, jobType);

        // Then - 验证计时器已注册（具体时间值难以精确测试）
        assertNotNull(sample);
    }

    @Test
    void testClearMetrics() {
        // Given
        String projectId = "test-project-6";
        String projectName = "测试项目6";
        String jobType = "month";
        String timezoneCode = "Asia/Shanghai";

        // When - 先记录一些指标
        metrics.recordSuccess(projectId, projectName, jobType, timezoneCode);
        metrics.recordFailure(projectId, projectName, jobType, timezoneCode, "错误");
        
        assertEquals(1.0, metrics.getSuccessCount(projectId, jobType));
        assertEquals(1.0, metrics.getFailureCount(projectId, jobType));

        // 清理指标
        metrics.clearMetrics(projectId, jobType);

        // Then - 指标应该被重置（新的计数器会从0开始）
        assertEquals(0.0, metrics.getSuccessCount(projectId, jobType));
        assertEquals(0.0, metrics.getFailureCount(projectId, jobType));
    }

    @Test
    void testTruncateErrorMessage() {
        // Given
        String projectId = "test-project-7";
        String projectName = "测试项目7";
        String jobType = "month";
        String timezoneCode = "Asia/Shanghai";
        
        // 创建一个超过100字符的错误信息
        String longErrorMessage = "这是一个非常长的错误信息".repeat(10);
        assertTrue(longErrorMessage.length() > 100);

        // When
        metrics.recordFailure(projectId, projectName, jobType, timezoneCode, longErrorMessage);

        // Then - 应该能正常记录，不会抛出异常
        double failureCount = metrics.getFailureCount(projectId, jobType);
        assertEquals(1.0, failureCount);
    }

    @Test
    void testGetMetricsSummary() {
        // Given
        String projectId1 = "test-project-8";
        String projectId2 = "test-project-9";
        String projectName = "测试项目";
        String jobType = "month";
        String timezoneCode = "Asia/Shanghai";

        // When - 记录一些指标
        metrics.recordSuccess(projectId1, projectName, jobType, timezoneCode);
        metrics.recordFailure(projectId2, projectName, jobType, timezoneCode, "错误");

        String summary = metrics.getMetricsSummary();

        // Then
        assertNotNull(summary);
        assertTrue(summary.contains("Operation Report Job Metrics Summary"));
        assertTrue(summary.contains("Success Counters"));
        assertTrue(summary.contains("Failure Counters"));
        assertTrue(summary.contains("Execution Timers"));
    }
}
